"use client";

import React from "react";
import { motion } from "framer-motion";
import { Quote } from "lucide-react";
import Image from 'next/image';

interface Testimonial {
  id: number;
  name: string;
  handle: string;
  avatar: string;
  content: string;
  verified?: boolean;
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: "Thib",
    handle: "@thibaultamartin",
    avatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face&auto=format",
    content:
      "loram ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    verified: true,
  },
  {
    id: 2,
    name: "Thib",
    handle: "@thibaultamartin",
    avatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face&auto=format",
    content:
      "loram ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    verified: true,
  },
  {
    id: 3,
     name: "Thib",
    handle: "@thibaultamartin",
    avatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face&auto=format",
    content:
      "loram ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    verified: true,
  },
  {
    id: 4,
    name: "Thib",
    handle: "@thibaultamartin",
    avatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face&auto=format",
    content:
      "loram ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    verified: true,
  },
  {
    id: 5,
    name: "Thib",
    handle: "@thibaultamartin",
    avatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face&auto=format",
    content:
      "loram ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    verified: true,
  },
  
];

const TestimonialCard: React.FC<{ testimonial: Testimonial }> = ({
  testimonial,
}) => {
  return (
    <div className="relative w-64 sm:w-72 md:w-80 bg-white border-2 border-blue-400 rounded-xl p-4 md:p-6 shadow-md mx-2 md:mx-4 flex-shrink-0 select-none">
      {/* Decorative quotes */}

      <Quote className="absolute -bottom-2 md:-bottom-4 right-2 md:right-4 text-blue-300 text-2xl md:text-4xl" />
      <Quote className="absolute -top-2 md:-top-4 left-2 md:left-4 text-blue-300 text-2xl md:text-4xl rotate-180" />

      <p className="text-gray-800 mb-4 md:mb-6 leading-relaxed text-sm md:text-base">
        {testimonial.content}
      </p>

      <div className="flex items-center space-x-3 md:space-x-4 mt-auto">
        <Image
          src={testimonial.avatar}
          alt={testimonial.name}
          width={40}
          height={40}
          className="w-10 h-10 md:w-14 md:h-14 rounded-full border-2 border-blue-400 object-cover"
        />
        <div>
          <p className="font-medium text-gray-900 text-sm md:text-base">{testimonial.name}</p>
          <p className="text-xs md:text-sm text-teal-600">{testimonial.handle}</p>
        </div>
      </div>
    </div>
  );
};

const TestimonialSection: React.FC = () => {
  const duplicated = [...testimonials, ...testimonials];

  return (
    <div className="py-6 md:py-8 bg-gray-50 overflow-hidden pointer-events-none select-none">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-8 md:mb-12 pointer-events-auto select-auto">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-3 md:mb-4">
            What Our Clients Say
          </h2>
          <p className="text-base md:text-lg text-gray-600 max-w-2xl mx-auto">
            Discover what our clients say about our 10+ years of MEP engineering
            excellence and commitment to delivering exceptional results
          </p>
        </div>
        

        <div className="relative overflow-hidden">
          <motion.div
            className="flex w-max relative my-3"
            animate={{ x: ["0%", "-50%"] }}
            transition={{
              repeat: Infinity,
              duration: 60,
              ease: "linear",
            }}
          >
            {duplicated.map((testimonial, idx) => (
              <TestimonialCard
                key={`${testimonial.id}-${idx}`}
                testimonial={testimonial}
              />
            ))}
          </motion.div>
          <div className="absolute top-0 left-0 h-full w-12 md:w-24 bg-gradient-to-r from-gray-50 via-white/70 to-white/0 pointer-events-none z-10" />

          {/* Right Gradient Smoke */}
          <div className="absolute top-0 right-0 h-full w-12 md:w-24 bg-gradient-to-l from-gray-50 via-white/70 to-white/0 pointer-events-none z-10" />
        </div>
      </div>
    </div>
  );
};

export default TestimonialSection;
