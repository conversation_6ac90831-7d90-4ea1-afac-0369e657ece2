"use client";

import React from "react";

import { Phone, Mail } from "lucide-react";
import Image from "next/image";

const ContactPage = () => {
  return (
    <div className="min-h-screen bg-white font-sans">
      {/* Hero Section */}
      <section className="relative py-12 md:py-16 lg:py-20 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/about/office-exterior.jpg"
            alt="JS Consultants Office - Contact Us"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-slate-900/70 to-slate-900/50"></div>
        </div>

        <div className="relative z-10 max-w-7xl flex mx-auto justify-start px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 md:space-y-6">
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-sans text-white">
              Contact Us
            </h1>
          </div>
        </div>
      </section>

      {/* Contact Form and Info */}
      <section className="py-12 md:py-16 lg:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-8 md:gap-12 lg:gap-16">
            <div className="space-y-6 md:space-y-8">
              <div>
                <h2 className="text-2xl md:text-3xl font-sans text-gray-900 mb-4 md:mb-6">
                  Get in Touch
                </h2>
                <p className="text-sm md:text-base text-gray-600 leading-relaxed mb-6 md:mb-8">
                  We&apos;re here to help you with all your electrical and MEP
                  engineering needs. Reach out to us through any of the
                  following channels.
                </p>
              </div>

              <div className="space-y-3 md:space-y-4">
                <div className="flex items-center gap-3 md:gap-4 mb-3 md:mb-4">
                  <Phone className="w-4 h-4 md:w-5 md:h-5 text-gray-600 flex-shrink-0" />
                  <span className="text-sm md:text-base text-gray-700">+91-98765-43210, +91-87654-32109</span>
                </div>
                <div className="flex items-center gap-3 md:gap-4">
                  <Mail className="w-4 h-4 md:w-5 md:h-5 text-gray-600 flex-shrink-0" />
                  <span className="text-sm md:text-base text-gray-700"><EMAIL></span>
                </div>
              </div>



            </div>
            <div>
                <h2 className="text-2xl md:text-3xl font-sans text-gray-800 mb-4 md:mb-6">
                  Reach us
                </h2>
                <div className="space-y-4 md:space-y-6">
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-2 text-sm md:text-base">
                      Office Address:
                    </h3>
                    <div className="text-sm md:text-base text-gray-700 leading-relaxed">
                      No: 8A, Shalom Cottage, Kalaimahal Nagar,
                      <br /> Mangadu, Chennai – 600122
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-2 text-sm md:text-base">
                      Registered Address:
                    </h3>
                    <div className="text-sm md:text-base text-gray-700 leading-relaxed">
                      No: 17/8, Shantham Colony,
                      <br /> Anna Nagar West Extension, Chennai – 600101
                    </div>
                  </div>
                </div>
              </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="pb-4 md:pb-5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">


          <div className="bg-white rounded-xl md:rounded-2xl shadow-lg overflow-hidden">
            <div className="h-64 md:h-80 lg:h-96 bg-gray-200 flex items-center justify-center">
              <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d4838.711699854051!2d80.11132231151396!3d13.025106187242198!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a52610016c47f5b%3A0x526c8679269d2fd6!2sJS%20CONSULTANTS!5e1!3m2!1sen!2sin!4v1753131072873!5m2!1sen!2sin" width="100%" height="100%" loading="lazy" referrerPolicy="no-referrer-when-downgrade"></iframe>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
