"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Image from "next/image";
import {
  Menu,
  Grid3X3,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { motion, AnimatePresence } from "motion/react";

import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetTrigger,
  SheetClose,
} from "@/components/ui/sheet";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import JsConsultants from "../intro/JsConsultants";
import {  NAVIGATION_ITEMS, HeaderServices } from "@/utils/constants";



// Enhanced Navigation Item Component with Framer Motion
const NavItem = ({ item }: { item: { name: string; href: string } }) => {
  const [isHovered, setIsHovered] = useState(false);
  const pathname = usePathname();

  // Check if current path matches the navigation item
  const isActive =
    pathname === item.href ||
    (item.href !== "/" && pathname.startsWith(item.href));

  return (
    <motion.div
      className="relative"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ y: -2 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <Link
        href={item.href}
        className={`relative flex items-center space-x-1 px-4 py-2 font-medium transition-colors duration-300 ${
          isActive ? "text-blue-600" : "text-gray-700 hover:text-blue-600"
        }`}
      >
        <span>{item.name}</span>
      </Link>

      {/* Bottom Line Indicator */}
      <motion.div
        className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500 origin-left"
        initial={{ scaleX: 0 }}
        animate={{
          scaleX: isHovered || isActive ? 1 : 0,
          opacity: isHovered || isActive ? 1 : 0,
        }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      />

      {/* Glowing Effect */}
      <AnimatePresence>
        {isHovered && (
          <motion.div
            className="absolute inset-0 bg-blue-500/10  rounded-t-lg -z-10"
            initial={{ opacity: 0, scale: 0.8, bottom: -10 }}
            animate={{ opacity: 1, scale: 1, bottom: 0 }}
            exit={{ opacity: 0, scale: 0.8, bottom: -10 }}
            transition={{ duration: 0.3 }}
          />
        )}
      </AnimatePresence>
    </motion.div>
  );
};

// Enhanced Services Dropdown Component
const ServicesDropdown = () => {
  const [hoveredService, setHoveredService] = useState(HeaderServices[0]);
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  // Check if we're on services page or any service-related page
  const isServicesActive = pathname.startsWith("/services");

  return (
    <NavigationMenuItem>
      <NavigationMenuTrigger
        className=" jagadeesh bg-transparent  hover:bg-blue-500/10 data-[state=open]:bg-blue-500/10 relative group"
        onMouseEnter={() => setIsOpen(true)}
        onMouseLeave={() => setIsOpen(false)}
      >
        <div className="flex items-center space-x-2">
          <Grid3X3 className="w-4 h-4" />
          <span>Services</span>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <ChevronDown className="w-4 h-4" />
          </motion.div>
        </div>

        {/* Bottom Line for Services */}
        <motion.div
          className=" jagadeesh absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500 origin-left"
          initial={{ scaleX: 0 }}
          animate={{ scaleX: isOpen || isServicesActive ? 1 : 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        />
      </NavigationMenuTrigger>

      <NavigationMenuContent>
        <motion.div
          className="w-full max-w-4xl p-4 md:p-6"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex flex-col lg:flex-row gap-4 lg:gap-6 w-full lg:w-[700px]">
            {/* Left side - Image with enhanced animations */}
            <div className="flex-shrink-0 w-full lg:w-1/2">
              <motion.div
                className="bg-gray-100 rounded-lg overflow-hidden shadow-lg relative"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                <motion.div
                  key={hoveredService.title}
                  initial={{ opacity: 0, scale: 1.1 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <Image
                    src={hoveredService.image || "/placeholder.svg"}
                    alt={hoveredService.title}
                    width={400}
                    height={400}
                    className="w-full h-auto object-cover aspect-square"
                  />
                </motion.div>
                <div className="p-4 bg-gray-50/20 absolute bottom-0 left-0 right-0">
                  <motion.h3
                    className="font-semibold text-lg text-gray-900"
                    key={`title-${hoveredService.title}`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    {hoveredService.title}
                  </motion.h3>
                  <motion.p
                    className="text-gray-600 text-sm mt-2 leading-relaxed"
                    key={`desc-${hoveredService.title}`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    {hoveredService.description}
                  </motion.p>
                </div>
              </motion.div>
            </div>

            {/* Right side - Menu items with enhanced hover effects */}
            <div className="flex-1 min-w-0 w-full lg:w-1/2">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                {HeaderServices.map((service, index) => (
                  <NavigationMenuLink
                    key={index}
                    asChild
                    onMouseEnter={() => setHoveredService(service)}
                  >
                    <motion.div
                      whileHover={{ scale: 1.02, x: 4 }}
                      transition={{ duration: 0.2 }}
                      className="hover:bg-transparent p-0 m-0"
                    >
                      <Link
                        href={service.href}
                        className="block rounded-lg py-3 px-2 hover:bg-blue-500/10 transition-all duration-300 border border-transparent hover:border-blue-200 group relative overflow-hidden"
                      >
                        <div className="relative z-10">
                          <div className="font-medium text-gray-900  group-hover:text-blue-700 transition-colors flex items-center justify-between">
                            <span>{service.title}</span>
                            <motion.div
                              className="opacity-0 group-hover:opacity-100"
                              initial={{ x: -10 }}
                              whileHover={{ x: 0 }}
                              transition={{ duration: 0.3 }}
                            >
                              <ChevronRight className="w-4 h-4 text-blue-600" />
                            </motion.div>
                          </div>
                        </div>
                      </Link>
                    </motion.div>
                  </NavigationMenuLink>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      </NavigationMenuContent>
    </NavigationMenuItem>
  );
};

export default function Header() {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  return (
    <nav className="border-b border-gray-400 sticky top-0 z-50 bg-white/80 backdrop-blur-md">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-14 md:h-16">
          {/* Logo with enhanced animations */}
          <motion.div
            className="px-2 md:px-3 h-full flex justify-center items-center relative overflow-hidden"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.3 }}
          >
            <motion.div
              className="absolute inset-0  opacity-0"
              whileHover={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            />
            <Link
              href="/"
              className="flex items-center space-x-2 relative z-10"
            >
              <div>
                <JsConsultants className="w-7 h-7 md:w-9 md:h-9" />
              </div>
              <span className="text-lg md:text-xl font-semibold text-gray-800 font-space-mono">
                Consultants
              </span>
            </Link>
          </motion.div>

          {/* Desktop Navigation with enhanced effects */}
          <div className="hidden lg:flex items-center space-x-2">
            <NavigationMenu>
              <NavigationMenuList>
                <ServicesDropdown />
              </NavigationMenuList>
            </NavigationMenu>

            {NAVIGATION_ITEMS.filter((item) => item.name !== "Services").map(
              (item) => (
                <NavItem key={item.name} item={item} />
              )
            )}
          </div>

          {/* Enhanced Enquire Now Button */}
          <div className="hidden md:flex h-full justify-center items-center p-2 md:p-4 relative overflow-hidden">
            <Link href="/enquire">
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="relative z-10 bg-blue-500 px-3 md:px-4 py-2 text-white rounded-lg font-medium hover:bg-blue-600 transition-colors duration-300 flex items-center space-x-2 group text-sm md:text-base"
              >
                <span>Enquire now</span>
              </motion.button>
            </Link>
          </div>

          {/* Enhanced Mobile Menu */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild className="md:hidden">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <motion.div
                  animate={{ rotate: isOpen ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Menu className="h-5 w-5 md:h-6 md:w-6" />
                </motion.div>
                <span className="sr-only">Open menu</span>
              </motion.button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[280px] sm:w-[350px] px-4">
              <motion.div
                className="flex flex-col space-y-4 mt-3"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                {NAVIGATION_ITEMS.map((item, index) => (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    {item.name === "Services" ? (
                      <div className="space-y-2">
                        <div className="text-xl font-medium text-gray-900 flex items-center">
                          <Grid3X3 className="w-4 h-4 mr-2" />
                          Services
                        </div>
                        <div className="pl-6 space-y-2">
                          {HeaderServices.map((service, serviceIndex) => (
                            <motion.div
                              key={serviceIndex}
                              whileHover={{ x: 4 }}
                              transition={{ duration: 0.2 }}
                            >
                              <SheetClose asChild>
                                <Link
                                  href={service.href}
                                  className=" text-lg text-gray-600 hover:text-blue-600 transition-colors flex items-center justify-between group"
                                >
                                  <span>{service.title}</span>
                                  <motion.div
                                    className="opacity-0 group-hover:opacity-100"
                                    initial={{ x: -10 }}
                                    whileHover={{ x: 0 }}
                                    transition={{ duration: 0.3 }}
                                  >
                                    <ChevronRight className="w-4 h-4" />
                                  </motion.div>
                                </Link>
                              </SheetClose>
                            </motion.div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <motion.div
                        whileHover={{ x: 4 }}
                        transition={{ duration: 0.2 }}
                      >
                        <SheetClose asChild>
                          <Link
                            href={item.href}
                            className={`text-xl font-medium transition-colors flex items-center justify-between group ${
                              pathname === item.href ||
                              (item.href !== "/" &&
                                pathname.startsWith(item.href))
                                ? "text-blue-600"
                                : "hover:text-blue-600"
                            }`}
                          >
                            <span>{item.name}</span>
                            <motion.div
                              className="opacity-0 group-hover:opacity-100"
                              initial={{ x: -10 }}
                              whileHover={{ x: 0 }}
                              transition={{ duration: 0.3 }}
                            >
                              <ChevronRight className="w-4 h-4" />
                            </motion.div>
                          </Link>
                        </SheetClose>
                      </motion.div>
                    )}
                  </motion.div>
                ))}

                <motion.div
                  className="pt-4 border-t border-gray-200"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.4 }}
                >
                  <SheetClose asChild>
                    <Link href="/enquire">
                      <motion.button
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:blue-700 transition-all duration-300 flex items-center justify-center space-x-2 group"
                      >
                        <span>Enquire now</span>
                        <motion.div
                          className="opacity-0 group-hover:opacity-100"
                          initial={{ x: -10 }}
                          whileHover={{ x: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <ChevronRight className="w-4 h-4" />
                        </motion.div>
                      </motion.button>
                    </Link>
                  </SheetClose>
                </motion.div>
              </motion.div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  );
}
