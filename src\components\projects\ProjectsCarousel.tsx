'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { ChevronLeft, ChevronRight, MapPin, Calendar, ArrowRight } from 'lucide-react'
import { PROJECTS } from '@/utils/constants'

interface ProjectsCarouselProps {
  autoPlay?: boolean
  interval?: number
  showControls?: boolean
}
import Image from 'next/image';
export function ProjectsCarousel({ 
  autoPlay = true, 
  interval = 6000,
  showControls = true 
}: ProjectsCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay)

  useEffect(() => {
    if (!isAutoPlaying) return

    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === PROJECTS.length - 1 ? 0 : prevIndex + 1
      )
    }, interval)

    return () => clearInterval(timer)
  }, [isAutoPlaying, interval])

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? PROJECTS.length - 1 : currentIndex - 1)
    setIsAutoPlaying(false)
  }

  const goToNext = () => {
    setCurrentIndex(currentIndex === PROJECTS.length - 1 ? 0 : currentIndex + 1)
    setIsAutoPlaying(false)
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'ongoing':
        return 'bg-blue-100 text-blue-800'
      case 'multiple projects':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'educational & hospitality':
        return 'bg-blue-100 text-blue-800'
      case 'industrial':
        return 'bg-orange-100 text-orange-800'
      case 'commercial':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="relative max-w-6xl mx-auto">
      {/* Main Carousel */}
      <div className="relative bg-white rounded-2xl shadow-xl overflow-hidden min-h-[500px]">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.6, ease: "easeInOut" }}
            className="grid grid-cols-1 lg:grid-cols-2 h-full"
          >
            {/* Project Image */}
            <div className="relative h-64 lg:h-full bg-gradient-to-br from-blue-500 to-slate-600">
              <Image 
                src={PROJECTS[currentIndex].image} 
                alt={PROJECTS[currentIndex].title}
                width={400}
                height={256}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.style.display = 'none'
                }}
              />
              
              {/* Overlay Badges */}
              <div className="absolute top-4 left-4 flex flex-col space-y-2">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(PROJECTS[currentIndex].category)}`}>
                  {PROJECTS[currentIndex].category}
                </span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(PROJECTS[currentIndex].status)}`}>
                  {PROJECTS[currentIndex].status}
                </span>
              </div>
            </div>

            {/* Project Content */}
            <div className="p-8 lg:p-12 flex flex-col justify-center">
              <div className="mb-6">
                <h3 className="text-2xl lg:text-3xl font-bold text-slate-800 mb-4">
                  {PROJECTS[currentIndex].title}
                </h3>
                
                <div className="flex flex-col space-y-2 mb-4">
                  <div className="flex items-center text-slate-600">
                    <MapPin className="w-4 h-4 mr-2" />
                    {PROJECTS[currentIndex].location}
                  </div>
                  <div className="flex items-center text-slate-600">
                    <Calendar className="w-4 h-4 mr-2" />
                    {PROJECTS[currentIndex].year}
                  </div>
                </div>
              </div>

              <p className="text-slate-700 leading-relaxed mb-6 text-lg">
                {PROJECTS[currentIndex].description}
              </p>

              {/* Services */}
              <div className="mb-8">
                <h4 className="font-semibold text-slate-800 mb-3">Services Provided:</h4>
                <div className="flex flex-wrap gap-2">
                  {PROJECTS[currentIndex].services.map((service, index) => (
                    <span 
                      key={index}
                      className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium"
                    >
                      {service}
                    </span>
                  ))}
                </div>
              </div>

              {/* CTA Button */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center group w-fit"
              >
                View Project Details
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </motion.button>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Navigation Buttons */}
        {showControls && (
          <>
            <button
              onClick={goToPrevious}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110 z-10"
            >
              <ChevronLeft className="w-6 h-6 text-slate-600" />
            </button>
            
            <button
              onClick={goToNext}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110 z-10"
            >
              <ChevronRight className="w-6 h-6 text-slate-600" />
            </button>
          </>
        )}
      </div>

      {/* Dots Indicator */}
      <div className="flex justify-center mt-6 space-x-2">
        {PROJECTS.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-200 ${
              index === currentIndex 
                ? 'bg-blue-600 scale-125' 
                : 'bg-slate-300 hover:bg-slate-400'
            }`}
          />
        ))}
      </div>

      {/* Progress Bar */}
      {isAutoPlaying && (
        <div className="mt-4 w-full bg-slate-200 rounded-full h-1 overflow-hidden">
          <motion.div
            className="h-full bg-gradient-to-r from-blue-500 to-blue-600"
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{ duration: interval / 1000, ease: "linear" }}
            key={currentIndex}
          />
        </div>
      )}

      {/* Project Counter */}
      <div className="text-center mt-4 text-slate-600">
        <span className="text-sm">
          Project {currentIndex + 1} of {PROJECTS.length}
        </span>
      </div>
    </div>
  )
}
