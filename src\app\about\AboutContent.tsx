"use client";

import React from "react";
import { motion } from "motion/react";
import { useInView } from "react-intersection-observer";

import { Download, Building } from "lucide-react";
import Image from "next/image";
import ClientsSection from "@/components/home/<USER>";
import { ArchitectsLogo } from "@/utils/constants";
const AboutContent = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });
  const [missionRef, missionInView] = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const leadership = [
    {
      name: "Mr. <PERSON><PERSON><PERSON><PERSON>",
      position: "CEO & Chief Consultant",
      experience: "24+ Years",
      specialization: "Electrical Switchgear Design & Consultancy",
      image: "/images/team/ceo.jpg",
      education: "B.E., Anna University (Main Campus – Guindy, Chennai)",
      profile:
        "Mr. <PERSON><PERSON><PERSON><PERSON> is a leading electrical switchgear design consultant and project execution expert with over 24 years of experience in electrical engineering. He has successfully delivered high-budget electrical infrastructure projects for luxury villas, hospitals, resorts, industrial factories, and data centers across India and international markets. His expertise focuses on creating cost-effective, future-ready electrical systems that meet global compliance standards for premium clients.",
      roles:
        "As the Chief Electrical Consultant, he combines strong academic foundation with deep technical expertise in switchgear design and electrical project management. He specializes in tailoring electrical systems that are energy-efficient, reliable, and globally compliant, making him a trusted electrical engineering advisor for high-net-worth individuals, premium property developers, and industrial enterprises seeking world-class electrical solutions.",

      focusAreas: [
        "High-end Residential Villas & Gated Communities",
        "Textile Industry Electrical Infrastructure",
        "Hospital & Healthcare Electrical Design",
        "Data Centers – High-Availability Systems",
        "Industrial Plants & Large-Scale Factories",
        "Luxury Resorts & Hotels",
      ],
    },
    {
      name: "Mr. S. Sankara Narayanan",
      position: "Partner & Project Head",
      experience: "14+ Years",
      specialization: "Electrical Design, Site Audits & Revamp Projects",
      image: "/images/team/engineer-1.jpg",
      education:
        "B.E., Electrical Engineering – MIT Campus, Anna University, Chennai",
      profile:
        "Mr. S. Sankara Narayanan is an electrical design specialist and project management expert with over 14 years of experience in electrical engineering consultancy. He specializes in new project design, comprehensive site audits, and electrical system revamp projects within the hospitality and allied consultancy domains. As co-founder of JS CONSULTANTS, he brings extensive MEP consulting expertise across South India, focusing on delivering innovative electrical solutions for commercial and residential projects.",
      roles:
        "As Partner & Project Head, he plays a crucial role in strategic project planning and operational delivery of electrical engineering projects. Known for his hands-on leadership approach and client-centric project management, he ensures every electrical project meets high technical standards while maintaining optimal performance within budget expectations. His expertise in electrical system optimization and project execution makes him a valuable leader in the electrical consultancy field.",

      focusAreas: [
        "Running Hospitals",
        "Operational IT Offices",
        "Luxury Bungalows",
        "Electrical Documentation & Layout Planning",
        "Technical Review of Existing Structures",
      ],
      projectExposure:
        "Extensive experience in handling MEP projects across villas, IT parks, commercial complexes, hospitals, and laboratories, particularly in South India.",
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="relative py-12 md:py-16 lg:py-20 overflow-hidden"
      >
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/about/office-exterior.jpg"
            alt="JS Consultants Office Building"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-slate-900/70 to-slate-900/50"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center space-y-4 md:space-y-6"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={heroInView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center px-3 md:px-4 py-2 bg-blue-600/20 text-blue-300 rounded-full text-xs md:text-sm font-medium backdrop-blur-sm border border-blue-400/30 mb-4"
            >
              <Building className="w-3 h-3 md:w-4 md:h-4 mr-2" />
              Professional MEP Engineering Firm
            </motion.div>

            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white">
              About <span className="text-blue-400">JS Consultants</span>
            </h1>
            <p className="text-base md:text-lg lg:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              A specialized electrical and MEP engineering firm committed to
              delivering the finest in design and execution for your most
              ambitious projects.
            </p>

            {/* Key Stats */}
            {/* <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={heroInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="grid grid-cols-3 gap-8 pt-8 max-w-2xl mx-auto"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-white">15+</div>
                <div className="text-sm text-gray-300">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">500+</div>
                <div className="text-sm text-gray-300">Projects Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">25+</div>
                <div className="text-sm text-gray-300">Expert Engineers</div>
              </div>
            </motion.div> */}
          </motion.div>
        </div>
      </section>

      {/* Company Introduction */}
      <section className="py-12 md:py-16 lg:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-8 md:gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={heroInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="space-y-4 md:space-y-6 order-last lg:order-first"
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900">
                Engineering Excellence Since 2009
              </h2>
              <div className="space-y-3 md:space-y-4 text-gray-600 leading-relaxed text-sm md:text-base">
                <p>
                  {" "}
                  Founded with a mission to redefine electrical and MEP
                  consultancy, JS Consultants has emerged as a trusted partner
                  for forward-thinking clients across industrial, commercial,
                  and institutional sectors.
                </p>
                <p>
                  Our multidisciplinary team blends deep technical expertise
                  with the latest design technologies to deliver
                  high-performance, code-compliant MEP solutions. Every project
                  we undertake is guided by a commitment to quality, safety,
                  efficiency, and sustainability.
                </p>{" "}
                <p>
                  From advanced data centers and healthcare facilities to modern
                  commercial developments, we bring precision, innovation, and
                  accountability to every engagement.
                </p>
              </div>

              {/* Key Achievements */}
              {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 pt-3 md:pt-4">
                <div className="flex items-center space-x-2 md:space-x-3">
                  <CheckCircle className="w-4 h-4 md:w-5 md:h-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700 text-xs md:text-sm">
                    ISO 9001:2015 Certified
                  </span>
                </div>
                <div className="flex items-center space-x-2 md:space-x-3">
                  <CheckCircle className="w-4 h-4 md:w-5 md:h-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700 text-xs md:text-sm">
                    LEED Accredited Professionals
                  </span>
                </div>
                <div className="flex items-center space-x-2 md:space-x-3">
                  <CheckCircle className="w-4 h-4 md:w-5 md:h-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700 text-xs md:text-sm">
                    Licensed Professional Engineers
                  </span>
                </div>
                <div className="flex items-center space-x-2 md:space-x-3">
                  <CheckCircle className="w-4 h-4 md:w-5 md:h-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700 text-xs md:text-sm">
                    24/7 Technical Support
                  </span>
                </div>
              </div> */}

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center px-4 md:px-6 py-2 md:py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-lg text-sm md:text-base"
              >
                <Download className="w-4 h-4 md:w-5 md:h-5 mr-2" />
                Download Company Profile
              </motion.button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={heroInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="relative order-first lg:order-last"
            >
              <div className="relative rounded-xl md:rounded-2xl overflow-hidden pointer-events-none">
                <Image
                  src="/images/about/retail.png"
                  alt="JS Consultants Modern Office Interior"
                  width={600}
                  height={400}
                  className="object-cover w-full h-auto"
                />
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section ref={missionRef} className="py-12 md:py-16 lg:py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={missionInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="p-6 md:p-8 border-b md:border-b border-gray-800"
            >
              <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-3 md:mb-4 font-space-mono flex items-center">
                <Image
                  src={"/images/about/mision.svg"}
                  alt="JS Consultants mission"
                  width={28}
                  height={28}
                  className="inline-block mr-3 md:mr-4 md:w-9 md:h-9"
                />
                Our Mission
              </h3>
              <p className="text-gray-600 leading-relaxed text-sm md:text-base">
                To deliver innovative, sustainable, and cost effective MEP
                engineering solutions that enhance performance, ensure occupant
                comfort, and promote energy efficiency while maintaining the
                highest standards of safety, quality, and client satisfaction.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={missionInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="p-6 md:p-8 border-b md:border-b md:border-l border-gray-900"
            >
              <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-3 md:mb-4 font-space-mono flex items-center">
                <Image
                  src={"/images/about/vision.svg"}
                  alt="JS Consultants vision"
                  width={28}
                  height={28}
                  className="inline-block mr-3 md:mr-4 md:w-9 md:h-9"
                />
                Our Vision
              </h3>
              <p className="text-gray-600 leading-relaxed text-sm md:text-base">
                To be a leading MEP consultancy firm recognized for engineering
                excellence, cutting edge technology, and a commitment to shaping
                smarter, greener, and more resilient built environments across
                India and beyond.
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={missionInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="p-6 md:p-8"
            >
              <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-3 md:mb-4 font-space-mono flex items-center">
                <Image
                  src={"/images/about/weare.svg"}
                  alt="JS Consultants vision"
                  width={28}
                  height={28}
                  className="inline-block mr-3 md:mr-4 md:w-9 md:h-9"
                />
                Our Values
              </h3>
              <p className="text-gray-600 leading-relaxed text-sm md:text-base">
                We maintain transparency, honesty, and ethical conduct in every
                project and partnership. We pursue engineering perfection
                through precision, quality, and continual improvement.
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={missionInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="p-6 md:p-8 md:border-l border-gray-900"
            >
              <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-3 md:mb-4 font-space-mono flex items-center">
                <Image
                  src={"/images/about/value.svg"}
                  alt="JS Consultants vision"
                  width={28}
                  height={28}
                  className="inline-block mr-3 md:mr-4 md:w-9 md:h-9"
                />
                How We Work
              </h3>
              <p className="text-gray-600 leading-relaxed text-sm md:text-base">
                We combine cutting-edge technology, experienced professionals,
                and collaborative processes to deliver efficient, scalable, and
                compliant solutions on time and within budget.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      <section>
        <div className="bg-gray-50 py-12 md:py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <div className="text-center mb-12 md:mb-16">
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 md:mb-4 font-mono">
                Meet Our Founders
              </h2>
              <p className="text-base md:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto">
                Leading with passion and purpose, our founders bring years of
                hands-on experience and a clear vision for the future of
                engineering and consulting.
              </p>
            </div>

            {/* Leadership Cards */}
            <div className="space-y-12 md:space-y-16">
              {leadership.map((leader, index) => (
                <div
                  key={leader.name}
                  className={`overflow-hidden transition-shadow duration-300 flex flex-col lg:flex-row min-h-[400px] md:min-h-[500px] ${
                    index === 1 ? "lg:flex-row-reverse" : "lg:flex-row"
                  }`}
                >
                  {/* Image Side */}
                  <div className="w-full lg:w-96 flex-shrink-0 order-first lg:order-none">
                    <div className="relative h-64 md:h-80 lg:h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      {/* Image placeholder */}
                      <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                        <span className="text-gray-500 text-sm md:text-lg">
                          Image: {leader.image}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Content Side */}
                  <div className="flex-1 p-6 md:p-8 lg:p-12 flex flex-col justify-center">
                    {/* Name and Position */}
                    <div className="mb-6 md:mb-8">
                      <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-2 tracking-wide">
                        {leader.name}
                      </h3>
                      <p className="text-base md:text-lg lg:text-xl text-orange-500 font-semibold uppercase tracking-wider">
                        {leader.position}
                      </p>
                    </div>

                    {/* Content */}
                    <div className="space-y-4 md:space-y-6">
                      <p className="text-gray-600 leading-relaxed text-sm md:text-base lg:text-lg">
                        {leader.profile}
                      </p>

                      <p className="text-gray-600 leading-relaxed text-sm md:text-base lg:text-lg">
                        {leader.roles}
                      </p>

                      {leader.projectExposure && (
                        <p className="text-gray-600 leading-relaxed text-sm md:text-base lg:text-lg">
                          {leader.projectExposure}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
      <section className="py-12 md:py-16 bg-gradient-to-b from-slate-50 to-white relative overflow-hidden">
        <div className="relative">
          <div className="text-center grid grid-cols-1 lg:grid-cols-2 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Main heading with gradient */}
            <h2 className="text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent mb-6 font-mono leading-tight text-center lg:text-start border-l-0 lg:border-l-4 border-gray-400 lg:pl-2">
              <span className="font-normal">Visionary Architects</span>
              <span className="block text-blue-600">Who Trust Us</span>
            </h2>
          </div>

          <ClientsSection clients={ArchitectsLogo} />
        </div>
      </section>
    </div>
  );
};

export default AboutContent;
