'use client'

import { motion } from 'motion/react'
import { MapPin, Calendar, CheckCircle, ArrowRight } from 'lucide-react'
import Image from 'next/image';

interface Project {
  id: number
  title: string
  location: string
  category: string
  description: string
  services: string[]
  image: string
  year: string
  status: string
}

interface ProjectShowcaseProps {
  project: Project
  delay?: number
}

export function ProjectShowcase({ project, delay = 0 }: ProjectShowcaseProps) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'ongoing':
        return 'bg-blue-100 text-blue-800'
      case 'multiple projects':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'educational & hospitality':
        return 'bg-blue-100 text-blue-800'
      case 'industrial':
        return 'bg-orange-100 text-orange-800'
      case 'commercial':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay }}
      whileHover={{ y: -10, scale: 1.02 }}
      className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
    >
      {/* Project Image */}
      <div className="relative h-64 bg-gradient-to-br from-blue-500 to-slate-600">
        <Image 
          src={project.image} 
          alt={project.title}
          width={400}
          height={256}
          className="w-full h-full object-cover"
          onError={(e) => {
            // Fallback to gradient background if image fails to load
            e.currentTarget.style.display = 'none'
          }}
        />
        
        {/* Overlay with Category */}
        <div className="absolute top-4 left-4">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(project.category)}`}>
            {project.category}
          </span>
        </div>

        {/* Status Badge */}
        <div className="absolute top-4 right-4">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)}`}>
            {project.status}
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Header */}
        <div className="mb-4">
          <h3 className="text-xl font-bold text-slate-800 mb-2">
            {project.title}
          </h3>
          <div className="flex items-center text-slate-600 text-sm mb-2">
            <MapPin className="w-4 h-4 mr-1" />
            {project.location}
          </div>
          <div className="flex items-center text-slate-600 text-sm">
            <Calendar className="w-4 h-4 mr-1" />
            {project.year}
          </div>
        </div>

        {/* Description */}
        <p className="text-slate-700 leading-relaxed mb-4">
          {project.description}
        </p>

        {/* Services */}
        <div className="mb-6">
          <h4 className="font-semibold text-slate-800 text-sm mb-2 flex items-center">
            <CheckCircle className="w-4 h-4 mr-1" />
            Services Provided:
          </h4>
          <div className="flex flex-wrap gap-2">
            {project.services.map((service, index) => (
              <span 
                key={index}
                className="bg-slate-100 text-slate-700 px-3 py-1 rounded-full text-sm font-medium"
              >
                {service}
              </span>
            ))}
          </div>
        </div>

        {/* View Details Button */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center group"
        >
          View Project Details
          <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
        </motion.button>
      </div>
    </motion.div>
  )
}
