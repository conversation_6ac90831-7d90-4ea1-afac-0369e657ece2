import React from "react";

import <PERSON> from "next/link";
import { But<PERSON> } from "../ui/button";

const CallToActionSection = () => {
  return (
    <div className="px-4 sm:px-6 md:px-8 lg:px-10 py-4 md:py-5">
      <div className="text-center mt-8 md:mt-12 lg:mt-16">
        <div className="bg-slate-900/90 rounded-xl md:rounded-2xl p-6 md:p-8 lg:p-12">
          <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-white mb-3 md:mb-4">
            Experience the JS Consultants Difference
          </h3>
          <p className="text-gray-50 mb-6 md:mb-8 max-w-5xl mx-auto text-sm md:text-base">
            Partner with a team that brings architectural precision, technical
            excellence, and innovative thinking to every MEP design. We’re
            committed to delivering efficient, future-ready solutions tailored
            to your project&apoes;s vision and scale.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center">
            <Link href="/enquire#enquiry-form">
              <Button size={"lg"}>Enquire Now</Button>
            </Link>
            <Link href={"/about"}>
              <Button variant={"ghost"} size={"lg"} className="text-white bg-gray-50/10">Learn About Us</Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallToActionSection;
